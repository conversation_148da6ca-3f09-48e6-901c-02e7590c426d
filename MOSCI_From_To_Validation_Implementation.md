# MOSCI From/To Institution Validation Implementation

## Summary
Added validation to prevent From and To institution values from being the same in the MOSCI screen.

## Changes Made

### 1. Real-time Validation on To Institution Dropdown Change
**File:** `MVC/Areas/Transfers/Views/Mosci/Index.cshtml`
**Location:** Lines 1264-1277

Added event handler that triggers when the To Institution dropdown (`SchdInst`) changes:
- Compares the From institution text value with the selected To institution text
- Shows alert if they are the same: "From and To institutions cannot be the same. Please select a different destination institution."
- Resets the To dropdown to empty selection if conflict detected

```javascript
// Add event handler for To Institution dropdown changes to validate against From Institution
$(document).on('change', 'select[id*="SchdInst"]', function() {
    var $row = $(this).closest('tr');
    var fromInstitution = $row.find('input[name*="frominsText"]').val();
    var toInstitutionDropdown = $(this);
    var toInstitutionText = toInstitutionDropdown.find('option:selected').text();
    
    // Check if From and To institutions are the same
    if (fromInstitution && toInstitutionText && fromInstitution.trim() === toInstitutionText.trim()) {
        alert('From and To institutions cannot be the same. Please select a different destination institution.');
        // Reset the dropdown to empty/default selection
        toInstitutionDropdown.val('');
        return false;
    }
});
```

### 2. Save Validation
**File:** `MVC/Areas/Transfers/Views/Mosci/Index.cshtml`
**Location:** Lines 1345-1367

Added validation during save operation:
- Checks all rows with valid data (non-empty Offender ID)
- Compares From and To institution values for each row
- Shows alert with specific row number if conflict found
- Prevents form submission if any conflicts exist

```javascript
// Validate that From and To institutions are not the same for any row
var hasFromToConflict = false;
var conflictRowNumber = 0;
$('#inmateTable tbody tr').each(function (index) {
    var $row = $(this);
    var offenderId = $row.find('input[id*="OffenderId"]').val();
    
    // Only validate rows that have data
    if (offenderId && offenderId.trim() !== '') {
        var fromInstitution = $row.find('input[name*="frominsText"]').val();
        var toInstitutionDropdown = $row.find('select[id*="SchdInst"]');
        var toInstitutionText = toInstitutionDropdown.find('option:selected').text();
        
        if (fromInstitution && toInstitutionText && fromInstitution.trim() === toInstitutionText.trim()) {
            hasFromToConflict = true;
            conflictRowNumber = index + 1;
            return false; // break out of each loop
        }
    }
});

if (hasFromToConflict) {
    alert('Row ' + conflictRowNumber + ': From and To institutions cannot be the same. Please select a different destination institution.');
    e.preventDefault();
    return false;
}
```

## Test Plan

### Test Case 1: Real-time Validation
1. Navigate to MOSCI screen
2. Search for and add an inmate (this populates the From institution)
3. Try to select the same institution in the To dropdown
4. **Expected Result:** Alert appears and To dropdown resets to empty

### Test Case 2: Save Validation
1. Navigate to MOSCI screen
2. Add an inmate with From institution populated
3. Select the same institution in To dropdown
4. Quickly click Save before the real-time validation triggers
5. **Expected Result:** Alert appears with row number and save is prevented

### Test Case 3: Valid Scenario
1. Navigate to MOSCI screen
2. Add an inmate with From institution populated
3. Select a different institution in To dropdown
4. Click Save
5. **Expected Result:** Save proceeds normally without validation errors

## Technical Notes

- **From Institution:** Readonly text input field (`input[name*="frominsText"]`)
- **To Institution:** Dropdown field (`select[id*="SchdInst"]`)
- **Validation Logic:** Text comparison using `.trim()` to handle whitespace
- **User Experience:** Immediate feedback with alert messages
- **Form Behavior:** Prevents submission when conflicts exist

## Compliance with Requirements

✅ Shows alert when From and To values are the same
✅ Uses JavaScript with confirmation popup
✅ Provides clear user feedback
✅ Prevents invalid data submission
✅ Maintains existing functionality
