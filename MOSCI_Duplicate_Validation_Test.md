# MOSCI Duplicate Offender Validation - Test Guide

## Overview
This document describes the duplicate offender validation functionality implemented for the MOSCI grid.

## Features Implemented

### 1. Real-time Validation on Tab/Enter
- When user enters an offender ID and presses Tab or Enter
- System checks if the same prefix + offender ID combination already exists in other rows
- Shows error message if duplicate is found
- Clears the offender ID field to prevent duplicate entry

### 2. Validation on Prefix Change
- When user changes the prefix dropdown
- System validates if the new prefix + existing offender ID creates a duplicate
- Shows error message and resets prefix if duplicate is found

### 3. Save Validation
- Before saving, system validates all rows for duplicates
- Prevents form submission if duplicates exist
- Shows detailed error message listing all duplicate offenders and their row numbers

## How to Test

### Test Case 1: Real-time Validation
1. Open the MOSCI page
2. Add a new row if needed
3. In the first row, select prefix "A" and enter offender ID "123456"
4. Press Tab or Enter - should auto-populate offender data
5. In the second row, select prefix "A" and enter offender ID "123456"
6. Press Tab or Enter
7. **Expected Result**: Error message appears: "Duplicate offender found! Offender A123456 already exists in row 1."
8. The offender ID field should be cleared

### Test Case 2: Prefix Change Validation
1. Continue from Test Case 1
2. In the second row, enter offender ID "123456" (same as first row)
3. Change the prefix dropdown to "A"
4. **Expected Result**: Error message appears and prefix resets to empty

### Test Case 3: Save Validation
1. Set up multiple rows with duplicate offenders
2. Click the Save button
3. **Expected Result**: 
   - Alert popup shows detailed duplicate information
   - Form submission is prevented
   - Error message banner appears at the top

### Test Case 4: Valid Data
1. Ensure all rows have unique prefix + offender ID combinations
2. Click Save
3. **Expected Result**: Form saves successfully without errors

## Technical Implementation

### JavaScript Functions Added:
- `validateDuplicateOffender(currentRowIndex, prefix, offenderId)` - Validates single offender
- `showValidationError(message)` - Shows error message banner
- `hideValidationError()` - Hides error message banner
- `validateAllRowsForDuplicates()` - Validates all rows before save

### Controller Methods Added:
- `ValidateDuplicateOffender()` - Server-side validation endpoint
- `ValidateForDuplicateOffenders()` - Helper method for save validation
- `DuplicateOffenderInfo` - Helper class for duplicate information

### Event Handlers:
- Enhanced `autoPopulateOffender()` function with duplicate validation
- Enhanced prefix dropdown change handler with validation
- Enhanced save button handler with duplicate validation

## Error Messages
- Real-time: "Duplicate offender found! Offender {ID} already exists in row {number}."
- Save: "Cannot save: Duplicate offenders found! Please remove duplicates before saving."

## Browser Compatibility
- Works with all modern browsers
- Uses jQuery for DOM manipulation
- Compatible with existing MOSCI functionality
