using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;



namespace Odrc.Dots.Entities.Transfers
{
    public class MosciData
    {
        public DateTime SchDate { get; set; }
        public int? SchdInst { get; set; }
        public int? Instno { get; set; }
        public string Instname { get; set; }
        public string Oid { get; set; } = "";
        public int Recno { get; set; }
        public string Descrl { get; set; } = "";
        public string Mant { get; set; } = "";
        public string Stationame { get; set; } = "";
        public DateTime SysDate { get; set; }
        public string Rowid { get; set; } = "";
        public string LastName { get; set; } = "";
        public string FirstName { get; set; } = "";
        public string InmateIdPrefix { get; set; } = "";
        public string OffenderId { get; set; } = "";
        //public List<SelectListItem> Prefix { get; set; } = new List<SelectListItem>();

        // Combined property for display purposes
        public string CombinedOffenderId { get; set; } = "";
        

        // Properties for row operations
        public bool IsSelected { get; set; }
        public bool IsMarkedForRemoval { get; set; }
        public bool IsMarkedForDeletion { get; set; }

    }

}
